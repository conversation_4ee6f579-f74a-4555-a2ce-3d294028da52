#!/usr/bin/env python3
"""
🇮🇱 Israel Defense Forces - Threat Detection GUI Application
Main GUI application for standalone EXE build in Visual Studio 2022
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QComboBox, 
                             QTextEdit, QFileDialog, QProgressBar, QGroupBox,
                             QGridLayout, QFrame, QScrollArea, QMessageBox,
                             QSplitter, QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

class AnalysisThread(QThread):
    """Background thread for threat analysis to keep GUI responsive"""
    analysis_complete = pyqtSignal(dict)
    analysis_error = pyqtSignal(str)

    def __init__(self, image_path, model_path):
        super().__init__()
        self.image_path = image_path
        self.model_path = model_path

    def run(self):
        """Run the analysis in background thread"""
        try:
            # Import classification modules
            from classifier.classifier import classify_image, get_weapon_info

            # Perform actual classification
            result, confidence, class_probs = classify_image(
                self.image_path,
                return_confidence=True
            )

            # Get detailed weapon information
            weapon_info = get_weapon_info(result)

            # Prepare results dictionary
            results = {
                'weapon': weapon_info['weapon'],
                'country': weapon_info['country'],
                'weapon_type': weapon_info['weapon_type'],
                'range': weapon_info['range'],
                'precision': weapon_info['precision'],
                'threat_level': weapon_info['threat_level'],
                'confidence': confidence,
                'israel_threat': weapon_info['israel_threat'],
                'class_probs': class_probs,
                'image_name': Path(self.image_path).name
            }

            self.analysis_complete.emit(results)

        except Exception as e:
            self.analysis_error.emit(str(e))

class ThreatDetectionGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_image_path = None
        self.current_model_path = None
        self.classification_results = []
        
        self.init_ui()
        self.load_available_models()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("🇮🇱 Israel Defense Forces - Threat Detection System")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 700)
        
        # Set application icon and style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            .danger {
                background-color: #f44336;
            }
            .danger:hover {
                background-color: #da190b;
            }
            .warning {
                background-color: #ff9800;
            }
            .warning:hover {
                background-color: #e68900;
            }
        """)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        self.create_header(main_layout)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Controls
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Results
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        # Status bar
        self.statusBar().showMessage("🇮🇱 Ready to protect Israel from threats")
        
    def create_header(self, layout):
        """Create the header section"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #2196F3;
                color: white;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        title = QLabel("🇮🇱 Israel Defense Forces - Threat Detection System")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white; margin: 5px;")
        
        subtitle = QLabel("🎯 Protecting the entire State of Israel from Iranian missiles and drones")
        subtitle.setFont(QFont("Arial", 12))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: white; margin: 5px;")
        
        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        layout.addWidget(header_frame)
        
    def create_left_panel(self):
        """Create the left control panel"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Model Selection Group
        model_group = QGroupBox("Model Selection")
        model_layout = QVBoxLayout(model_group)
        
        self.model_combo = QComboBox()
        self.model_combo.setMinimumHeight(35)
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        model_layout.addWidget(QLabel("Select AI Detection Model:"))
        model_layout.addWidget(self.model_combo)
        
        upload_model_btn = QPushButton("Upload New Model")
        upload_model_btn.clicked.connect(self.upload_model)
        model_layout.addWidget(upload_model_btn)
        
        left_layout.addWidget(model_group)
        
        # Image Upload Group
        image_group = QGroupBox("Image Upload")
        image_layout = QVBoxLayout(image_group)
        
        upload_btn = QPushButton("Upload Missile/Drone Photo")
        upload_btn.setMinimumHeight(50)
        upload_btn.setStyleSheet("font-size: 16px; font-weight: bold;")
        upload_btn.clicked.connect(self.upload_image)
        
        upload_folder_btn = QPushButton("📁 Upload Multiple Images")
        upload_folder_btn.clicked.connect(self.upload_folder)
        
        image_layout.addWidget(upload_btn)
        image_layout.addWidget(upload_folder_btn)
        
        # Current image display
        self.image_label = QLabel("No image selected")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumHeight(200)
        self.image_label.setStyleSheet("""
            border: 2px dashed #cccccc;
            border-radius: 10px;
            background-color: white;
        """)
        image_layout.addWidget(self.image_label)
        
        left_layout.addWidget(image_group)
        
        # Analysis Group
        analysis_group = QGroupBox("Threat Analysis")
        analysis_layout = QVBoxLayout(analysis_group)
        
        self.analyze_btn = QPushButton("ANALYZE THREAT")
        self.analyze_btn.setMinimumHeight(50)
        self.analyze_btn.setStyleSheet("""
            background-color: #f44336;
            font-size: 16px;
            font-weight: bold;
        """)
        self.analyze_btn.clicked.connect(self.analyze_threat)
        self.analyze_btn.setEnabled(False)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        analysis_layout.addWidget(self.analyze_btn)
        analysis_layout.addWidget(self.progress_bar)
        
        left_layout.addWidget(analysis_group)
        
        # Add stretch to push everything to top
        left_layout.addStretch()
        
        return left_widget
        
    def create_right_panel(self):
        """Create the right results panel"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Results tabs
        self.tabs = QTabWidget()
        
        # Threat Assessment Tab
        threat_tab = QWidget()
        threat_layout = QVBoxLayout(threat_tab)
        
        self.threat_results = QTextEdit()
        self.threat_results.setFont(QFont("Consolas", 11))
        self.threat_results.setPlainText("""
🇮🇱 ISRAEL DEFENSE FORCES - THREAT DETECTION SYSTEM

Instructions:
1. Select an AI model from the dropdown
2. Upload a photo of suspected missile/drone
3. Click 'ANALYZE THREAT' for instant identification

Supported Threats:
• Iranian ballistic missiles (Fateh-110, Zolfaghar, etc.)
• Kamikaze drones (Shahed-136, Geran-2)
• Artillery rockets (Fajr-5, Zelzal-2)
• Cruise missiles (Hoveyzeh, Paveh)

Protected Areas:
• Jerusalem • Tel Aviv • Haifa • Beersheba
• Ramat Gan • Petah Tikva • ALL Israeli cities

        """)
        threat_layout.addWidget(self.threat_results)
        
        self.tabs.addTab(threat_tab, " Assessment Results")
        
        # History Tab
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)

        # History controls
        history_controls = QHBoxLayout()

        clear_history_btn = QPushButton("🗑️ Clear History")
        clear_history_btn.clicked.connect(self.clear_history)
        clear_history_btn.setStyleSheet("background-color: #ff9800; color: white; font-weight: bold;")

        refresh_history_btn = QPushButton("🔄 Refresh")
        refresh_history_btn.clicked.connect(self.refresh_history)
        refresh_history_btn.setStyleSheet("background-color: #4caf50; color: white; font-weight: bold;")

        history_controls.addWidget(clear_history_btn)
        history_controls.addWidget(refresh_history_btn)
        history_controls.addStretch()

        history_layout.addLayout(history_controls)

        self.history_results = QTextEdit()
        self.history_results.setFont(QFont("Consolas", 10))
        self.history_results.setPlainText("Analysis history will appear here...")
        history_layout.addWidget(self.history_results)

        self.tabs.addTab(history_tab, "Analysis History")
        
        right_layout.addWidget(self.tabs)
        
        return right_widget
        
    def load_available_models(self):
        """Load available AI models"""
        self.model_combo.clear()
        self.model_combo.addItem("Select AI Model...")
        
        models_dir = Path("models")
        if models_dir.exists():
            model_files = []
            for ext in ['.onnx', '.pt', '.pth', '.pkl', '.h5']:
                model_files.extend(models_dir.glob(f"*{ext}"))
            
            for model_file in model_files:
                self.model_combo.addItem(f"🤖 {model_file.name}", str(model_file))
        
        if self.model_combo.count() == 1:
            self.model_combo.addItem("⚠️ No models found - Upload a model first")
            
    def upload_model(self):
        """Upload a new AI model"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Upload AI Model for Threat Detection",
            "",
            "AI Models (*.onnx *.pt *.pth *.pkl *.h5);;All Files (*)"
        )
        
        if file_path:
            models_dir = Path("models")
            models_dir.mkdir(exist_ok=True)
            
            # Copy model to models directory
            import shutil
            model_name = Path(file_path).name
            dest_path = models_dir / model_name
            shutil.copy2(file_path, dest_path)
            
            self.load_available_models()
            
            # Select the newly uploaded model
            for i in range(self.model_combo.count()):
                if model_name in self.model_combo.itemText(i):
                    self.model_combo.setCurrentIndex(i)
                    break
                    
            QMessageBox.information(self, "Success", f"✅ Model uploaded: {model_name}")
            
    def upload_image(self):
        """Upload a single image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "📸 Upload Missile/Drone Photo for Analysis",
            "",
            "Images (*.jpg *.jpeg *.png *.bmp *.heic *.webp);;All Files (*)"
        )

        if file_path:
            # Clear previous results when new image is uploaded
            self.clear_previous_results()

            self.current_image_path = file_path
            self.display_image(file_path)
            self.analyze_btn.setEnabled(True)

            # Update status to show new image is ready for analysis
            self.statusBar().showMessage(f"📷 New image loaded: {Path(file_path).name} - Ready for analysis")

    def clear_previous_results(self):
        """Clear previous analysis results when new image is uploaded"""
        # Clear the threat results display
        self.threat_results.setPlainText("""
🇮🇱 ISRAEL DEFENSE FORCES - THREAT DETECTION SYSTEM
======================================================================

📷 Ready for Analysis

Instructions:
1. Select an AI model from the dropdown
2. Upload a photo of suspected missile/drone
3. Click 'ANALYZE THREAT' for instant identification

Supported Threats:
• Iranian ballistic missiles (Fateh-110, Zolfaghar, etc.)
• Kamikaze drones (Shahed-136, Geran-2)
• Artillery rockets (Fajr-5, Zelzal-2)
• Cruise missiles (Hoveyzeh, Paveh)

Protected Areas:
• Jerusalem • Tel Aviv • Haifa • Beersheba
• Ramat Gan • Petah Tikva • ALL Israeli cities

        """)

        # Switch back to the threat assessment tab
        self.tabs.setCurrentIndex(0)

        # Update status
        self.statusBar().showMessage("🔄 Previous results cleared - Ready for new analysis")

    def force_fresh_analysis(self):
        """Force a fresh analysis by clearing any cached data"""
        # Clear any potential cached classification results
        self.classification_results.clear()

        # Force garbage collection to clear any cached embeddings
        import gc
        gc.collect()

        # Clear the results display to show we're starting fresh
        self.threat_results.setPlainText("🔄 Starting fresh analysis...\n\nPlease wait while we analyze the threat...")

        # Update status
        self.statusBar().showMessage("🔄 Forcing fresh analysis - No cached results")

    def upload_folder(self):
        """Upload multiple images from folder"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "📁 Select Folder with Missile/Drone Photos"
        )
        
        if folder_path:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.heic', '.webp']
            image_files = []
            
            for ext in image_extensions:
                image_files.extend(Path(folder_path).glob(f"*{ext}"))
                image_files.extend(Path(folder_path).glob(f"*{ext.upper()}"))
            
            if image_files:
                QMessageBox.information(
                    self, 
                    "Batch Upload", 
                    f"📁 Found {len(image_files)} images\n🔄 Processing batch analysis..."
                )
                # TODO: Implement batch processing
            else:
                QMessageBox.warning(self, "No Images", "❌ No image files found in selected folder")
                
    def display_image(self, image_path):
        """Display the selected image"""
        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            # Scale image to fit label while maintaining aspect ratio
            scaled_pixmap = pixmap.scaled(
                self.image_label.size(), 
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
            self.image_label.setText("")
        else:
            self.image_label.setText("❌ Failed to load image")
            
    def on_model_changed(self):
        """Handle model selection change"""
        current_text = self.model_combo.currentText()
        if current_text and not current_text.startswith("Select") and not current_text.startswith("⚠️"):
            self.current_model_path = self.model_combo.currentData()
            self.statusBar().showMessage(f"🤖 Model selected: {current_text}")
            
    def analyze_threat(self):
        """Analyze the uploaded image for threats"""
        if not self.current_image_path:
            QMessageBox.warning(self, "No Image", "❌ Please upload an image first")
            return

        if not self.current_model_path:
            QMessageBox.warning(self, "No Model", "❌ Please select an AI model first")
            return

        # Clear any cached results and force fresh analysis
        self.force_fresh_analysis()

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.analyze_btn.setEnabled(False)
        self.statusBar().showMessage("🔍 Analyzing threat... Please wait")

        # Start analysis in separate thread to keep GUI responsive
        self.analysis_thread = AnalysisThread(self.current_image_path, self.current_model_path)
        self.analysis_thread.analysis_complete.connect(self.show_analysis_results)
        self.analysis_thread.analysis_error.connect(self.show_analysis_error)
        self.analysis_thread.start()
        
    def show_analysis_results(self, results):
        """Show the analysis results from real classification"""
        # Hide progress
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)

        # Extract results
        israel_threat = results['israel_threat']

        # Format results text
        results_text = f"""
🇮🇱 ISRAEL DEFENSE FORCES - THREAT ANALYSIS RESULTS
{'='*70}

📷 Image: {results['image_name']}
🤖 AI Model: {self.model_combo.currentText()}
⏰ Analysis Time: Real-time

🚨 THREAT IDENTIFICATION:
🚀 Detected Weapon: {results['weapon']}
🏴 Country/Origin: {results['country']}
🎯 Weapon Type: {results['weapon_type']}
📏 Range: {results['range']}
🎯 Precision: {results['precision']}
⚠️  Threat Level: {results['threat_level']}
📊 AI Confidence: {results['confidence']:.1%}

🇮🇱 ISRAELI TERRITORY THREAT ASSESSMENT:
🎯 Threat Coverage: {israel_threat['threat_level']}
🏙️  Cities at Risk: {israel_threat['coverage']}
📏 Effective Range: {israel_threat['range_km']}km

🏘️  Threatened Cities:"""

        # Add threatened cities
        if israel_threat['threatened_cities']:
            cities_text = ""
            for i, city in enumerate(israel_threat['threatened_cities'][:12]):  # Show first 12
                if i % 3 == 0:
                    cities_text += "\n   • "
                cities_text += f"{city} • "
            if len(israel_threat['threatened_cities']) > 12:
                cities_text += f"\n   • ... and {len(israel_threat['threatened_cities']) - 12} more cities"
            results_text += cities_text.rstrip(" • ")

        # Add strategic sites
        if israel_threat['strategic_sites_threatened']:
            results_text += "\n\n🏭 Strategic Sites at Risk:"
            for site in israel_threat['strategic_sites_threatened']:
                results_text += f"\n   • {site}"

        # Add threat level warnings
        if israel_threat['range_km'] >= 350:
            results_text += "\n\n🚨 NATIONAL THREAT: Can reach entire State of Israel including Eilat"
        elif israel_threat['range_km'] >= 200:
            results_text += "\n\n🚨 MAJOR THREAT: Can reach most Israeli cities"
        elif israel_threat['range_km'] >= 70:
            results_text += "\n\n⚠️  CENTRAL ISRAEL THREAT: Can reach Tel Aviv, Jerusalem, Haifa"

        # Add weapon-specific warnings
        if 'hypersonic' in results['weapon_type'].lower():
            results_text += "\n⚡ HYPERSONIC THREAT: Iron Dome may be insufficient"
        if 'kamikaze' in results['weapon_type'].lower() or 'drone' in results['weapon_type'].lower():
            results_text += "\n🛸 DRONE THREAT: Low-altitude, potential swarm attack"
        if 'MIRV' in results['weapon_type']:
            results_text += "\n💥 MIRV THREAT: Multiple warheads, extremely dangerous"

        # Add alternative predictions
        if results['class_probs']:
            results_text += "\n\n📈 Alternative Identifications:"
            sorted_probs = sorted(results['class_probs'].items(), key=lambda x: x[1], reverse=True)
            for i, (weapon, prob) in enumerate(sorted_probs[:5]):
                if i == 0:
                    continue  # Skip top prediction (already shown)
                results_text += f"\n   {weapon}: {prob:.1%}"

        results_text += f"""

{'='*70}
📞 REPORT TO IDF: *3456 or local emergency services
🚨 SHARE WITH SECURITY FORCES if confirmed threat
{'='*70}
🇮🇱 Analysis completed - Stay vigilant! 🇮🇱
        """

        self.threat_results.setPlainText(results_text)
        self.tabs.setCurrentIndex(0)  # Switch to threat assessment tab

        # Add to history with detailed information and unique timestamp
        from datetime import datetime
        import random

        timestamp = datetime.now().strftime("%H:%M:%S")
        analysis_id = random.randint(1000, 9999)  # Unique ID for this analysis

        # Create detailed history entry
        history_entry = f"[{timestamp}] ID:{analysis_id} | {results['image_name']} → {results['weapon']} ({results['confidence']:.1%}) | {results['country']} | {results['weapon_type']}\n"

        # Clear default text and add new entry at the top
        current_history = self.history_results.toPlainText()
        if "Analysis history will appear here..." in current_history:
            # First entry - replace default text
            self.history_results.setPlainText(f"🇮🇱 THREAT ANALYSIS HISTORY\n{'='*50}\n\n{history_entry}")
        else:
            # Add to existing history at the top
            self.history_results.setPlainText(f"🇮🇱 THREAT ANALYSIS HISTORY\n{'='*50}\n\n{history_entry}\n{current_history.split('='*50)[-1].strip()}")

        # Force refresh of the history display
        self.history_results.update()
        self.history_results.repaint()

        self.statusBar().showMessage(f"🚨 Threat analysis completed: {results['weapon']} detected")

    def clear_history(self):
        """Clear the analysis history"""
        self.history_results.setPlainText("Analysis history will appear here...")
        self.statusBar().showMessage("🗑️ Analysis history cleared")

    def refresh_history(self):
        """Refresh the history display"""
        self.history_results.update()
        self.history_results.repaint()
        self.statusBar().showMessage("🔄 History display refreshed")

    def show_analysis_error(self, error_message):
        """Show analysis error"""
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)

        error_text = f"""
❌ ANALYSIS ERROR
{'='*50}

🚨 Failed to analyze image: {Path(self.current_image_path).name}

Error Details:
{error_message}

💡 Troubleshooting:
• Ensure AI model is properly loaded
• Check image format (JPG, PNG, HEIC supported)
• Verify image is clear and well-lit
• Try a different AI model

🔧 Technical Support:
• Check models/ folder for valid AI models
• Ensure all dependencies are installed
• Restart application if needed

{'='*50}
        """

        self.threat_results.setPlainText(error_text)
        self.statusBar().showMessage("❌ Analysis failed - Check error details")

        QMessageBox.critical(self, "Analysis Error", f"❌ Analysis failed:\n{error_message}")

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Israel Defense Forces - Threat Detection")
    app.setApplicationVersion("1.0")
    
    # Set application icon
    app.setWindowIcon(QIcon("assets/icon.ico"))  # Add your icon file
    
    window = ThreatDetectionGUI()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
